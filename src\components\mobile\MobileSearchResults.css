/* 移动端搜索结果组件样式 */
.mobile-search-results {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 主容器头部 */
.search-results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  min-height: 44px; /* 触摸友好的最小高度 */
}

.search-results-header:hover {
  background-color: #f8f9fa;
}

.search-results-header:active {
  background-color: #e9ecef;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0; /* 防止文本溢出 */
}

.search-icon {
  width: 18px;
  height: 18px;
  color: #666;
  flex-shrink: 0;
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.source-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
  white-space: nowrap;
  flex-shrink: 0;
}

.chevron-icon {
  width: 20px;
  height: 20px;
  color: #666;
  transition: transform 0.3s ease-in-out;
  flex-shrink: 0;
}

.chevron-icon.expanded {
  transform: rotate(180deg);
}

/* 展开状态提示 */
.expand-status {
  padding: 8px 20px;
  font-size: 12px;
  color: #9ca3af;
  background-color: #f9fafb;
  text-align: center;
  transition: all 0.2s ease;
}

/* 结果容器 - 关键的折叠动画实现 */
.results-container {
  overflow: hidden;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
}

.results-container.collapsed {
  max-height: 0;
  opacity: 0;
}

.results-container.expanded {
  max-height: 2000px; /* 足够大的值以容纳所有内容 */
  opacity: 1;
}

.results-content {
  padding: 0;
}

/* 单个结果项 */
.result-item {
  background: transparent;
}

.result-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  min-height: 44px; /* 触摸友好的最小高度 */
}

.result-item-header:hover {
  background-color: #f8f9fa;
}

.result-item-header:active {
  background-color: #e9ecef;
}

.result-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* 防止文本溢出 */
}

/* 状态圆点 */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot-red {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.status-dot-blue {
  background-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.status-dot-green {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.status-dot-orange {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1);
}

.status-dot-purple {
  background-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.result-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  overflow-wrap: break-word;
}

.result-chevron {
  width: 16px;
  height: 16px;
  color: #999;
  transition: transform 0.3s ease-in-out;
  flex-shrink: 0;
}

.result-chevron.expanded {
  transform: rotate(180deg);
}

/* 结果项内容 - 关键的折叠动画实现 */
.result-item-content {
  overflow: hidden;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
}

.result-item-content.collapsed {
  max-height: 0;
  opacity: 0;
}

.result-item-content.expanded {
  max-height: 1000px; /* 足够大的值以容纳内容 */
  opacity: 1;
}

.result-content-inner {
  padding: 0 20px 16px 20px;
}

/* 结果描述 */
.result-description {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 12px;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 详细信息列表 */
.result-details {
  margin-bottom: 16px;
}

.details-list {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.detail-item {
  position: relative;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 8px;
  padding-left: 0;
  word-break: break-word;
  overflow-wrap: break-word;
}

.detail-item::before {
  content: '•';
  color: #999;
  position: absolute;
  left: -16px;
  top: 0;
}

/* 来源链接 */
.result-sources {
  padding-top: 12px;
}

.sources-title {
  font-size: 13px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.source-item {
  margin-bottom: 8px;
}

.source-link {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-decoration: none;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 44px; /* 触摸友好的最小高度 */
}

.source-link:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
}

.source-link:active {
  transform: translateY(0);
  background-color: #dee2e6;
}

.source-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 2px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.source-domain {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 遵循用户偏好：PC段落和标题垂直边距减半 */
@media (min-width: 769px) {
  .result-description p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .result-description h1,
  .result-description h2,
  .result-description h3,
  .result-description h4,
  .result-description h5,
  .result-description h6 {
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }
}

/* 移动端优化 (320px - 768px) */
@media (max-width: 768px) {
  .mobile-search-results {
    border-radius: 10px;
  }

  .search-results-header,
  .result-item-header {
    padding: 14px 16px;
    min-height: 44px;
  }

  .result-content-inner {
    padding: 0 16px 14px 16px;
  }

  .search-title {
    font-size: 15px;
  }

  .source-count {
    font-size: 13px;
  }

  .result-title {
    font-size: 14px;
  }

  .result-description,
  .detail-item {
    font-size: 13px;
    line-height: 1.5;
  }

  .source-title {
    font-size: 12px;
  }

  .source-domain {
    font-size: 11px;
  }

  /* 调整间距以适应小屏幕 */
  .header-left {
    gap: 6px;
  }

  .result-header-left {
    gap: 10px;
  }

  .expand-status {
    padding: 6px 16px;
    font-size: 11px;
  }
}

/* 超小屏幕优化 (320px - 480px) */
@media (max-width: 480px) {
  .mobile-search-results {
    border-radius: 8px;
  }

  .search-results-header,
  .result-item-header {
    padding: 12px 14px;
    min-height: 44px;
  }

  .result-content-inner {
    padding: 0 14px 12px 14px;
  }

  .search-title {
    font-size: 14px;
  }

  .source-count {
    font-size: 12px;
  }

  .result-title {
    font-size: 13px;
  }

  .result-description,
  .detail-item {
    font-size: 12px;
    line-height: 1.5;
  }

  .source-title {
    font-size: 11px;
  }

  .source-domain {
    font-size: 10px;
  }

  /* 进一步调整间距 */
  .header-left {
    gap: 4px;
  }

  .result-header-left {
    gap: 8px;
  }

  .expand-status {
    padding: 4px 14px;
    font-size: 10px;
  }

  /* 调整图标大小 */
  .search-icon {
    width: 16px;
    height: 16px;
  }

  .chevron-icon {
    width: 18px;
    height: 18px;
  }

  .result-chevron {
    width: 14px;
    height: 14px;
  }

  .status-dot {
    width: 6px;
    height: 6px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .search-results-header,
  .result-item-header {
    padding: 12px 16px;
  }

  .result-content-inner {
    padding: 0 16px 12px 16px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .status-dot {
    box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.1);
  }
}

/* 浅色模式优化 */
.mobile-search-results {
  background: #ffffff;
}

.search-results-header {
  background: #fafbfc;
}

.search-results-header:hover {
  background-color: #f1f3f4;
}

.search-results-header:active {
  background-color: #e8eaed;
}

.search-title {
  color: #1f2937;
}

.source-count {
  color: #6b7280;
}

.search-icon,
.chevron-icon {
  color: #6b7280;
}

.expand-status {
  background-color: #f9fafb;
  color: #9ca3af;
}

.result-item {
  background: transparent;
}

.result-item-header:hover {
  background-color: #f9fafb;
}

.result-item-header:active {
  background-color: #f3f4f6;
}

.result-title {
  color: #111827;
}

.result-chevron {
  color: #9ca3af;
}

.result-description,
.detail-item {
  color: #4b5563;
}

.sources-title {
  color: #6b7280;
}

.source-link {
  background-color: #f9fafb;
}

.source-link:hover {
  background-color: #f3f4f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.source-link:active {
  background-color: #e5e7eb;
}

.source-title {
  color: #111827;
}

.source-domain {
  color: #6b7280;
}
