# PC端Header滚动隐藏效果实现总结

## 功能描述
为PC端的Header组件添加了和ChatDetailHeader一样的滚动隐藏效果，当用户向下滚动时Header会隐藏，向上滚动时会重新显示。

## 实现细节

### 1. 状态管理
在ChatDetail.tsx中添加了滚动状态管理：

```tsx
// PC端Header滚动状态管理
const [isHeaderVisible, setIsHeaderVisible] = useState(true);
const [lastScrollY, setLastScrollY] = useState(0);
```

### 2. 滚动监听逻辑
添加了useEffect来监听页面滚动事件：

```tsx
// PC端Header滚动监听
useEffect(() => {
  const handleScroll = () => {
    const currentScrollY = window.scrollY;

    // 向下滚动超过100px时隐藏，向上滚动时显示
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      setIsHeaderVisible(false);
    } else {
      setIsHeaderVisible(true);
    }

    setLastScrollY(currentScrollY);
  };

  // 添加滚动监听器
  window.addEventListener('scroll', handleScroll, { passive: true });

  // 清理函数
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
}, [lastScrollY]);
```

### 3. Header组件样式更新
为Header组件添加了动态CSS类和过渡动画：

```tsx
{/* Header 组件 - 置顶显示，支持滚动隐藏 */}
{currentApp && (
  <div className={`md:block hidden fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-in-out ${
    isHeaderVisible ? 'transform translate-y-0' : 'transform -translate-y-full'
  }`}>
    <Header
      onMenuClick={() => setSidebarOpen(!sidebarOpen)}
      currentApp={currentApp}
      onAppSelect={onAppSelect}
      appList={appList}
      xAiApi={xAiApi}
      subStatusDetail={appListSub.get(currentApp.appNameEn) || null}
      showSubscriptionModal={showSubscriptionModal}
      setShowSubscriptionModal={setShowSubscriptionModal}
    />
  </div>
)}
```

## 功能特性

### 滚动行为
- **向下滚动**: 当用户向下滚动超过100px时，Header会向上滑出隐藏
- **向上滚动**: 当用户向上滚动时，Header会立即向下滑入显示
- **顶部位置**: 当页面在顶部时（scrollY < 100px），Header始终显示

### 动画效果
- **过渡时间**: 300ms
- **缓动函数**: ease-in-out
- **变换效果**: translateY (向上/向下滑动)

### 响应式设计
- **PC端**: 启用滚动隐藏效果 (`md:block hidden`)
- **移动端**: Header组件不显示，使用ChatDetailHeader代替

## 技术实现

### 滚动检测逻辑
```javascript
// 判断滚动方向和距离
if (currentScrollY > lastScrollY && currentScrollY > 100) {
  // 向下滚动且超过100px -> 隐藏Header
  setIsHeaderVisible(false);
} else {
  // 向上滚动或在顶部区域 -> 显示Header
  setIsHeaderVisible(true);
}
```

### CSS动画类
```css
/* 显示状态 */
transform translate-y-0

/* 隐藏状态 */
transform -translate-y-full

/* 过渡动画 */
transition-transform duration-300 ease-in-out
```

## 用户体验优化

1. **平滑过渡**: 300ms的过渡动画确保Header的显示/隐藏不会突兀
2. **智能触发**: 只有向下滚动超过100px才隐藏，避免轻微滚动时的频繁切换
3. **即时响应**: 向上滚动时立即显示Header，方便用户快速访问导航
4. **性能优化**: 使用`{ passive: true }`选项优化滚动监听性能

## 与ChatDetailHeader的一致性

现在PC端的Header组件和移动端的ChatDetailHeader具有相同的滚动隐藏行为：
- 相同的滚动检测逻辑
- 相同的动画过渡效果
- 相同的用户交互体验

## 兼容性

- ✅ 不影响现有功能
- ✅ 保持响应式设计
- ✅ 与ChatDetailHeader行为一致
- ✅ 性能优化的滚动监听
