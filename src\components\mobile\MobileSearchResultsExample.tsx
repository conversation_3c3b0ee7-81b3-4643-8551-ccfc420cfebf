import React, { useState } from 'react';
import MobileSearchResults from './MobileSearchResults';

// 示例：如何在AssistantMessage中集成MobileSearchResults组件
const MobileSearchResultsExample: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 示例数据：模拟工作流节点
  const mockWorkflowNodes = [
    {
      id: 'node-1',
      type: 'llm',
      title: '翻译和理解问题',
      status: 'success' as const,
      created_at: Date.now() - 3000,
      outputs: {
        answer: '正在分析用户的问题并进行语言处理...',
        details: [
          '识别问题语言：中文',
          '提取关键词：移动端、搜索结果、组件',
          '理解用户意图：创建移动端适配的搜索组件'
        ],
        sources: [
          {
            title: '移动端UI设计指南',
            url: 'https://example.com/mobile-ui-guide',
            domain: 'example.com'
          }
        ]
      },
      displayCategory: 'left' as const
    },
    {
      id: 'node-2',
      type: 'tool',
      title: '总结价格动态',
      status: 'running' as const,
      created_at: Date.now() - 2000,
      outputs: {
        answer: '正在收集和分析最新的价格信息...',
        details: [
          '截至2025年7月3日，美国鸡蛋价格为每打2.56美元',
          '比前一天高，但低于今年早些时候的峰值',
          '过去一个月价格上涨0.79%，同比去年同期上涨1.22%'
        ],
        sources: [
          {
            title: '2025蛋价危机：美国人纷纷跨境消费鸡蛋寻求专业商应对措施2025',
            url: 'https://sohu.com/example',
            domain: 'sohu.com'
          },
          {
            title: '美国鸡蛋价格高涨：高流感、消费者恐慌与供体...',
            url: 'https://forwardpathway.com/example',
            domain: 'forwardpathway.com'
          }
        ]
      },
      displayCategory: 'right' as const
    },
    {
      id: 'node-3',
      type: 'llm',
      title: '评估消费者反应',
      status: 'error' as const,
      created_at: Date.now() - 1000,
      outputs: {
        answer: '分析消费者对价格变化的反应和购买行为...',
        details: [
          '消费者开始寻找替代蛋白质来源',
          '部分地区出现囤积现象',
          '餐饮业调整菜单以应对成本上升'
        ]
      },
      displayCategory: 'right' as const,
      error: '分析过程中遇到数据源连接问题'
    }
  ];

  // 示例数据：模拟搜索结果
  const mockSearchResults = [
    {
      id: 'search-1',
      title: '移动端UI组件设计最佳实践',
      content: '本文介绍了移动端UI组件设计的核心原则和最佳实践，包括触摸友好的交互设计、响应式布局等。',
      details: [
        '触摸目标最小尺寸应为44px',
        '使用合适的字体大小和行高',
        '确保足够的对比度和可读性',
        '优化加载性能和动画效果'
      ],
      sources: [
        {
          title: 'Mobile UI Design Guidelines',
          url: 'https://developer.apple.com/design/human-interface-guidelines/',
          domain: 'developer.apple.com'
        },
        {
          title: 'Material Design for Mobile',
          url: 'https://material.io/design',
          domain: 'material.io'
        }
      ],
      statusColor: 'green' as const
    }
  ];

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleNodeClick = (nodeId: string) => {
    console.log('节点被点击:', nodeId);
    // 这里可以添加节点点击的处理逻辑
    // 例如：滚动到对应位置、显示详细信息等
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <h1 style={{ marginBottom: '20px', color: '#333' }}>移动端搜索结果组件示例</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#666', fontSize: '16px' }}>使用工作流节点数据：</h2>
        <MobileSearchResults
          title="DeepSearch"
          workflowNodes={mockWorkflowNodes}
          isExpanded={isExpanded}
          onToggle={handleToggle}
          onNodeClick={handleNodeClick}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#666', fontSize: '16px' }}>使用搜索结果数据：</h2>
        <MobileSearchResults
          title="搜索结果"
          results={mockSearchResults}
          totalSources={5}
          isExpanded={true}
          onToggle={() => {}}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#666', fontSize: '16px' }}>混合数据源：</h2>
        <MobileSearchResults
          title="综合搜索"
          results={mockSearchResults}
          rightPanelNodes={mockWorkflowNodes.filter(node => node.displayCategory === 'right')}
          isExpanded={true}
          onToggle={() => {}}
          onNodeClick={handleNodeClick}
        />
      </div>

      <div style={{ 
        marginTop: '40px', 
        padding: '20px', 
        backgroundColor: 'white', 
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#333', marginBottom: '10px' }}>集成说明：</h3>
        <ul style={{ color: '#666', lineHeight: '1.6' }}>
          <li>组件自动处理工作流节点和搜索结果的合并显示</li>
          <li>优先显示 rightPanelNodes，然后是 leftPanelNodes，最后是 workflowNodes</li>
          <li>支持独立的展开/折叠状态管理</li>
          <li>提供节点点击回调，便于与现有系统集成</li>
          <li>完全响应式设计，适配各种移动设备</li>
          <li>遵循用户偏好的设计规范</li>
        </ul>
      </div>
    </div>
  );
};

export default MobileSearchResultsExample;
