import React, { useState } from 'react';
import MobileSearchResults from './MobileSearchResults';

// 演示页面：展示移动端搜索结果组件的效果
const MobileSearchResultsDemo: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [simulationStep, setSimulationStep] = useState(0);

  // 模拟工作流执行状态变化
  const getNodeStatus = (nodeIndex: number, step: number) => {
    if (step === 0) return 'pending' as const;
    if (step <= nodeIndex) return 'pending' as const;
    if (step === nodeIndex + 1) return 'running' as const;
    return 'success' as const;
  };

  // 模拟真实的工作流节点数据
  const mockWorkflowNodes = [
    {
      id: 'node-1',
      type: 'llm',
      title: '需求分析',
      status: getNodeStatus(0, simulationStep),
      created_at: Date.now() - 5000,
      outputs: {
        answer: '您好！为了确保我们的工作流程和分析，这里是最新的用户需求分析结果，这样我们可以更好地理解您的具体需求。',
        details: [
          '您的研究领域(Research_Field): 请注意检查是否有适当的关键字或术语来确定的具体学术领域。学科分支或研究方向。',
          '您的研究基础(Research_Foundation): 请审查是否存在关于已发表的文章列表或其他对研究成果的相关说明文字，特别是那些基础研究成果的地方。',
          '您的研究条件(Research_Condition): 请检查是否有关于实验设备、资金支持等研究条件的信息。'
        ]
      },
      displayCategory: 'left' as const
    },
    {
      id: 'node-2',
      type: 'tool',
      title: '翻译和理解问题',
      status: getNodeStatus(1, simulationStep),
      created_at: Date.now() - 4000,
      outputs: {
        answer: '正在分析用户的问题并进行语言处理，确保准确理解用户意图...',
        details: [
          '识别问题语言：中文',
          '提取关键词：移动端、搜索结果、组件、DeepSearch',
          '理解用户意图：创建移动端适配的搜索结果展示组件',
          '分析技术要求：响应式设计、折叠动画、触摸友好'
        ],
        sources: [
          {
            title: '移动端UI设计最佳实践指南',
            url: 'https://developer.apple.com/design/human-interface-guidelines/',
            domain: 'developer.apple.com'
          }
        ]
      },
      displayCategory: 'right' as const
    },
    {
      id: 'node-3',
      type: 'tool',
      title: '总结价格动态',
      status: getNodeStatus(2, simulationStep),
      created_at: Date.now() - 3000,
      outputs: {
        answer: '正在收集和分析最新的价格信息，包括市场趋势和消费者反应...',
        details: [
          '截至2025年7月3日，美国鸡蛋价格为每打2.56美元，比前一天高，但低于今年早些时候的峰值。过去一个月价格上涨0.79%，同比去年同期上涨1.22%',
          '预测显示，除非产蛋母鸡数量复苏正常，涨价趋势可能延续至2025年下半年。高流感疫情和供应链问题是主要原因'
        ],
        sources: [
          {
            title: '2025蛋价危机：美国人纷纷跨境消费鸡蛋寻求专业商应对措施2025',
            url: 'https://sohu.com/example',
            domain: 'sohu.com'
          },
          {
            title: '美国鸡蛋价格高涨：高流感、消费者恐慌与供体...',
            url: 'https://forwardpathway.com/example',
            domain: 'forwardpathway.com'
          },
          {
            title: '价格高涨近期供货蛋价应对缺陷成美国2025年...',
            url: 'https://wenxuecity.com/example',
            domain: 'wenxuecity.com'
          }
        ]
      },
      displayCategory: 'right' as const
    },
    {
      id: 'node-4',
      type: 'llm',
      title: '评估消费者反应',
      status: getNodeStatus(3, simulationStep),
      created_at: Date.now() - 2000,
      outputs: {
        answer: '分析消费者对价格变化的反应和购买行为，以及对整个市场的影响...',
        details: [
          '消费者开始寻找替代蛋白质来源，如豆制品和坚果',
          '部分地区出现囤积现象，进一步推高价格',
          '餐饮业调整菜单以应对成本上升，减少含蛋料理',
          '零售商推出促销活动以维持销量'
        ]
      },
      displayCategory: 'right' as const,
      error: '分析过程中遇到数据源连接问题，正在重试...'
    }
  ];

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleNodeClick = (nodeId: string) => {
    console.log('节点被点击:', nodeId);
    // 这里可以添加节点点击的处理逻辑
    alert(`点击了节点: ${nodeId}`);
  };

  // 模拟工作流执行
  const startSimulation = () => {
    setSimulationStep(0);
    const interval = setInterval(() => {
      setSimulationStep(prev => {
        if (prev >= 5) {
          clearInterval(interval);
          return prev;
        }
        return prev + 1;
      });
    }, 2000); // 每2秒执行下一步
  };

  // 重置模拟
  const resetSimulation = () => {
    setSimulationStep(0);
  };

  // 快速测试：模拟节点状态快速变化
  const quickTest = () => {
    setSimulationStep(0);
    setTimeout(() => setSimulationStep(1), 500);
    setTimeout(() => setSimulationStep(2), 1000);
    setTimeout(() => setSimulationStep(3), 1500);
    setTimeout(() => setSimulationStep(4), 2000);
    setTimeout(() => setSimulationStep(5), 2500);
  };

  return (
    <div style={{ 
      padding: '0', 
      backgroundColor: '#f5f5f5', 
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* 页面标题 */}
      <div style={{ 
        padding: '20px', 
        backgroundColor: 'white', 
        borderBottom: '1px solid #e0e0e0',
        position: 'sticky',
        top: 0,
        zIndex: 10
      }}>
        <h1 style={{ 
          margin: 0, 
          fontSize: '18px', 
          fontWeight: '600', 
          color: '#333' 
        }}>
          移动端搜索结果组件演示
        </h1>
        <p style={{
          margin: '8px 0 0 0',
          fontSize: '14px',
          color: '#666'
        }}>
          仿照DeepSearch设计，支持双级折叠和平滑动画
        </p>

        {/* 模拟控制按钮 */}
        <div style={{
          marginTop: '12px',
          display: 'flex',
          gap: '8px'
        }}>
          <button
            onClick={startSimulation}
            style={{
              padding: '6px 12px',
              fontSize: '12px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            开始模拟执行
          </button>
          <button
            onClick={resetSimulation}
            style={{
              padding: '6px 12px',
              fontSize: '12px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            重置
          </button>
          <button
            onClick={quickTest}
            style={{
              padding: '6px 12px',
              fontSize: '12px',
              backgroundColor: '#f59e0b',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            快速测试
          </button>
          <span style={{
            fontSize: '12px',
            color: '#666',
            alignSelf: 'center'
          }}>
            步骤: {simulationStep}/5
          </span>
        </div>
      </div>
      
      {/* 移动端搜索结果组件 */}
      <MobileSearchResults
        title="DeepSearch"
        workflowNodes={mockWorkflowNodes}
        isExpanded={isExpanded}
        onToggle={handleToggle}
        onNodeClick={handleNodeClick}
      />

      {/* 功能说明 */}
      <div style={{ 
        margin: '16px', 
        padding: '16px', 
        backgroundColor: 'white', 
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ 
          margin: '0 0 12px 0', 
          fontSize: '16px', 
          fontWeight: '600', 
          color: '#333' 
        }}>
          功能特性
        </h3>
        <ul style={{
          margin: 0,
          paddingLeft: '20px',
          color: '#666',
          fontSize: '14px',
          lineHeight: '1.6'
        }}>
          <li>🎨 仿照DeepSearch的UI设计风格</li>
          <li>📱 完全响应式，适配各种移动设备</li>
          <li>🔄 双级折叠：整体容器 + 单个结果项</li>
          <li>✨ 平滑的CSS过渡动画（300-400ms）</li>
          <li>🎯 触摸友好的交互设计（最小44px点击区域）</li>
          <li>🌈 彩色状态圆点（橙色=等待，蓝色=运行中，绿色=成功，红色=错误）</li>
          <li>🔗 支持来源链接和详细信息展示</li>
          <li>🔧 兼容现有AssistantMessage的工作流节点数据</li>
          <li>🤖 智能展开：运行中节点自动展开，完成后保持展开状态</li>
          <li>⚡ 防抖逻辑：避免频繁切换，500ms延迟执行</li>
          <li>👆 用户优先级：手动操作后暂停自动逻辑12-15秒</li>
          <li>🔄 智能恢复：自动逻辑在合适时机重新启用</li>
          <li>📊 详细日志：控制台显示自动逻辑的执行状态</li>
        </ul>
      </div>

      {/* 使用说明 */}
      <div style={{ 
        margin: '16px', 
        padding: '16px', 
        backgroundColor: 'white', 
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ 
          margin: '0 0 12px 0', 
          fontSize: '16px', 
          fontWeight: '600', 
          color: '#333' 
        }}>
          操作指南
        </h3>
        <ol style={{
          margin: 0,
          paddingLeft: '20px',
          color: '#666',
          fontSize: '14px',
          lineHeight: '1.6'
        }}>
          <li>点击"开始模拟执行"观察智能展开效果</li>
          <li>点击"快速测试"观察快速状态变化的处理</li>
          <li>运行中的节点会自动展开显示详细内容</li>
          <li>完成的节点保持展开状态，方便查看结果</li>
          <li><strong>用户优先级测试</strong>：在模拟执行过程中手动点击标题栏或节点标题</li>
          <li>手动操作后自动逻辑会暂停12-15秒，避免与用户意图冲突</li>
          <li>观察控制台日志了解自动逻辑的暂停和恢复过程</li>
          <li>观察状态圆点的颜色变化（橙色→蓝色→绿色）</li>
          <li>尝试在不同屏幕尺寸下查看响应式效果</li>
        </ol>
      </div>

      {/* 底部间距 */}
      <div style={{ height: '40px' }}></div>
    </div>
  );
};

export default MobileSearchResultsDemo;
