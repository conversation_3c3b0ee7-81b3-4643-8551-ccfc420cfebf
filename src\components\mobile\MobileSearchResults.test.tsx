import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import MobileSearchResults from './MobileSearchResults';

// Mock the i18n hook
jest.mock('../../i18n/simple-hooks', () => ({
  useSimpleTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'mobile.search.title': 'DeepSearch',
        'mobile.search.sourcesCount': '{count} sources',
        'mobile.search.clickToToggle': 'Click to expand/collapse',
        'mobile.search.sourceLinks': 'Source Links:',
        'mobile.search.running': 'Running',
        'mobile.search.completed': 'Completed',
        'mobile.search.success': 'Success',
        'mobile.search.error': 'Error',
        'mobile.search.pending': 'Pending',
      };
      return translations[key] || key;
    }
  })
}));

// Mock icons
jest.mock('../icons/Icons', () => ({
  ChevronDownIcon: ({ className }: { className: string }) => (
    <div data-testid="chevron-icon" className={className}>▼</div>
  ),
  SearchIcon: ({ className }: { className: string }) => (
    <div data-testid="search-icon" className={className}>🔍</div>
  ),
}));

describe('MobileSearchResults', () => {
  const mockWorkflowNodes = [
    {
      id: 'node-1',
      type: 'search',
      title: 'Search Node',
      status: 'running' as const,
      created_at: Date.now(),
      outputs: {
        answer: 'This is a test search result with some content.',
        sources: [
          {
            title: 'Test Source 1',
            url: 'https://example.com/1',
            domain: 'example.com'
          }
        ]
      }
    },
    {
      id: 'node-2',
      type: 'analysis',
      title: 'Analysis Node',
      status: 'success' as const,
      created_at: Date.now() + 1000,
      outputs: {
        answer: 'Analysis complete.',
        details: ['Detail 1', 'Detail 2']
      }
    }
  ];

  const defaultProps = {
    title: 'Test Search Results',
    totalSources: 2,
    workflowNodes: mockWorkflowNodes,
    isExpanded: false,
    onToggle: jest.fn(),
    onNodeClick: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders component with title and source count', () => {
    render(<MobileSearchResults {...defaultProps} />);
    
    expect(screen.getByText('Test Search Results')).toBeInTheDocument();
    expect(screen.getByText('• 2 sources')).toBeInTheDocument();
  });

  test('toggles container expansion when header is clicked', () => {
    render(<MobileSearchResults {...defaultProps} />);
    
    const header = screen.getByRole('button', { name: /click to expand\/collapse/i });
    fireEvent.click(header);
    
    expect(defaultProps.onToggle).toHaveBeenCalledTimes(1);
  });

  test('supports keyboard navigation', () => {
    render(<MobileSearchResults {...defaultProps} />);
    
    const header = screen.getByRole('button', { name: /click to expand\/collapse/i });
    fireEvent.keyDown(header, { key: 'Enter' });
    
    expect(defaultProps.onToggle).toHaveBeenCalledTimes(1);
  });

  test('displays workflow nodes as result items', () => {
    render(<MobileSearchResults {...defaultProps} isExpanded={true} />);
    
    expect(screen.getByText('Search Node')).toBeInTheDocument();
    expect(screen.getByText('Analysis Node')).toBeInTheDocument();
  });

  test('shows status dots with correct colors', () => {
    render(<MobileSearchResults {...defaultProps} isExpanded={true} />);
    
    const statusDots = screen.getAllByTitle(/running|success/i);
    expect(statusDots).toHaveLength(2);
  });

  test('expands individual items when clicked', async () => {
    render(<MobileSearchResults {...defaultProps} isExpanded={true} />);
    
    const itemHeader = screen.getByRole('button', { name: /search node/i });
    fireEvent.click(itemHeader);
    
    await waitFor(() => {
      expect(screen.getByText('This is a test search result with some content.')).toBeInTheDocument();
    });
  });

  test('displays source links when available', async () => {
    render(<MobileSearchResults {...defaultProps} isExpanded={true} />);
    
    const itemHeader = screen.getByRole('button', { name: /search node/i });
    fireEvent.click(itemHeader);
    
    await waitFor(() => {
      expect(screen.getByText('Source Links:')).toBeInTheDocument();
      expect(screen.getByText('Test Source 1')).toBeInTheDocument();
    });
  });

  test('handles empty results gracefully', () => {
    const { container } = render(
      <MobileSearchResults 
        {...defaultProps} 
        workflowNodes={[]} 
        results={[]} 
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('applies custom className', () => {
    const { container } = render(
      <MobileSearchResults {...defaultProps} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('mobile-search-results', 'custom-class');
  });

  test('uses internationalization when enabled', () => {
    render(<MobileSearchResults {...defaultProps} useI18n={true} />);
    
    // The component should use the mocked translation function
    expect(screen.getByText('• 2 sources')).toBeInTheDocument();
  });
});
